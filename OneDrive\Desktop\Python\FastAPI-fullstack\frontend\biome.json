{"$schema": "https://biomejs.dev/schemas/1.6.1/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["node_modules", "src/routeTree.gen.ts", "playwright.config.ts", "playwright-report"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "style": {"noNonNullAssertion": "off"}}}, "formatter": {"indentStyle": "space"}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded"}}}