name: Labels
on:
  pull_request_target:
    types:
      - opened
      - synchronize
      - reopened
      # For label-checker
      - labeled
      - unlabeled

jobs:
  labeler:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
    - uses: actions/labeler@v5
      if: ${{ github.event.action != 'labeled' && github.event.action != 'unlabeled' }}
    - run: echo "Done adding labels"
  # Run this after labeler applied labels
  check-labels:
    needs:
      - labeler
    permissions:
      pull-requests: read
    runs-on: ubuntu-latest
    steps:
      - uses: docker://agilepathway/pull-request-label-checker:latest
        with:
          one_of: breaking,security,feature,bug,refactor,upgrade,docs,lang-all,internal
          repo_token: ${{ secrets.GITHUB_TOKEN }}
